// Authentication types
export interface LoginRequest {
  grant_type: string;
  username: string;
  password: string;
  scope?: string;
  client_id?: string;
  client_secret?: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
}

export interface User {
  id: string;
  username: string;
  email?: string;
  role: string;
  created_at?: string;
  updated_at?: string;
}

// Tenant types
export interface Tenant {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
  owner_id?: string;
}

export interface CreateTenantRequest {
  name: string;
  description?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Form types
export interface LoginFormData {
  username: string;
  password: string;
}

export interface CreateUserFormData {
  username: string;
  email: string;
  password: string;
  role: string;
}

// Auth context types
export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (credentials: LoginFormData) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// Environment types
export interface EnvironmentConfig {
  API_BASE_URL: string;
  API_TIMEOUT: number;
}
