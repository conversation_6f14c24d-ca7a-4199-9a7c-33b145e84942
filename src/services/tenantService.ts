import baseHttp from './baseHttp';
import { Tenant, CreateTenantRequest } from '../types';

class TenantService {
  /**
   * Get all tenants
   */
  async getTenants(): Promise<Tenant[]> {
    const response = await baseHttp.get<Tenant[]>('/tenants/list');
    return response.data;
  }

  /**
   * Get tenant by ID
   */
  async getTenant(id: string): Promise<Tenant> {
    const response = await baseHttp.get<Tenant>(`/tenants/${id}`);
    return response.data;
  }

  /**
   * Create new tenant
   */
  async createTenant(tenantData: CreateTenantRequest): Promise<Tenant> {
    const response = await baseHttp.post<Tenant>('/tenants', tenantData);
    return response.data;
  }

  /**
   * Update tenant
   */
  async updateTenant(id: string, tenantData: Partial<CreateTenantRequest>): Promise<Tenant> {
    const response = await baseHttp.put<Tenant>(`/tenants/${id}`, tenantData);
    return response.data;
  }

  /**
   * Delete tenant
   */
  async deleteTenant(id: string): Promise<void> {
    await baseHttp.delete(`/tenants/${id}`);
  }
}

const tenantService = new TenantService();
export default tenantService;
