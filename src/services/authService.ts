import baseHttp from './baseHttp';
import { LoginRequest, LoginResponse, User, LoginFormData } from '@/types';

class AuthService {
  /**
   * Login user with username and password
   */
  async login(credentials: LoginFormData): Promise<LoginResponse> {
    const loginData: LoginRequest = {
      grant_type: 'password',
      username: credentials.username,
      password: credentials.password,
      scope: '',
      client_id: 'string',
      client_secret: 'string',
    };

    // Use form-encoded data as per the API specification
    const formData = new URLSearchParams();
    Object.entries(loginData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const response = await baseHttp.post<LoginResponse>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    // Store token in localStorage
    if (response.data.access_token) {
      localStorage.setItem('access_token', response.data.access_token);
    }

    return response.data;
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<User> {
    const response = await baseHttp.get<User>('/auth/me');
    return response.data;
  }

  /**
   * Get all users (admin only)
   */
  async getUsers(): Promise<User[]> {
    const response = await baseHttp.get<User[]>('/auth/users');
    return response.data;
  }

  /**
   * Logout user
   */
  logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  /**
   * Get stored user
   */
  getStoredUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  /**
   * Store user data
   */
  storeUser(user: User): void {
    localStorage.setItem('user', JSON.stringify(user));
  }
}

export default new AuthService();
