import React from 'react';
import { <PERSON><PERSON> as AntButton, ButtonProps as AntButtonProps } from 'antd';

interface ButtonProps extends Omit<AntButtonProps, 'variant'> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  children: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  children, 
  style,
  ...props 
}) => {
  const getButtonStyle = () => {
    const baseStyle = {
      borderRadius: '8px',
      fontWeight: 600,
      height: '40px',
      ...style,
    };

    switch (variant) {
      case 'secondary':
        return {
          ...baseStyle,
          background: '#f8f9fa',
          borderColor: '#dee2e6',
          color: '#495057',
        };
      case 'danger':
        return {
          ...baseStyle,
          background: '#dc3545',
          borderColor: '#dc3545',
        };
      case 'ghost':
        return {
          ...baseStyle,
          background: 'transparent',
          borderColor: '#667eea',
          color: '#667eea',
        };
      default:
        return {
          ...baseStyle,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',
        };
    }
  };

  return (
    <AntButton
      type={variant === 'primary' ? 'primary' : variant === 'danger' ? 'primary' : 'default'}
      style={getButtonStyle()}
      {...props}
    >
      {children}
    </AntButton>
  );
};

export default Button;
