import React, { useState } from 'react';
import { Form, Input, Button, Alert, Typography, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { LoginFormData } from '@/types';

const { Title, Text } = Typography;

interface LoginFormProps {
  onSubmit: (values: LoginFormData) => Promise<void>;
  loading?: boolean;
  error?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit, loading = false, error }) => {
  const [form] = Form.useForm();

  const handleSubmit = async (values: LoginFormData) => {
    try {
      await onSubmit(values);
    } catch (err) {
      // Error handling is done in parent component
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.formWrapper}>
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
          {/* Logo and Title */}
          <div style={styles.header}>
            <img 
              src="/final-logo-black.png" 
              alt="Company Logo" 
              style={styles.logo}
              onError={(e) => {
                // Fallback to favicon if main logo fails
                (e.target as HTMLImageElement).src = "/favicon-black.png";
              }}
            />
            <Title level={2} style={styles.title}>
              Multi-Tenant System
            </Title>
            <Text type="secondary" style={styles.subtitle}>
              Sign in to your account
            </Text>
          </div>

          {/* Error Alert */}
          {error && (
            <Alert
              message="Login Failed"
              description={error}
              type="error"
              showIcon
              style={styles.alert}
            />
          )}

          {/* Login Form */}
          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
            style={styles.form}
            initialValues={{
              username: 'admin',
              password: 'admin'
            }}
          >
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Please enter your username!' },
                { min: 3, message: 'Username must be at least 3 characters!' }
              ]}
            >
              <Input
                prefix={<UserOutlined style={styles.inputIcon} />}
                placeholder="Enter your username"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please enter your password!' },
                { min: 3, message: 'Password must be at least 3 characters!' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={styles.inputIcon} />}
                placeholder="Enter your password"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<LoginOutlined />}
                style={styles.submitButton}
                block
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </Form.Item>
          </Form>

          {/* Demo Credentials */}
          <div style={styles.demoCredentials}>
            <Text type="secondary" style={styles.demoText}>
              Demo Credentials: admin / admin
            </Text>
          </div>
        </Space>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    padding: '20px',
  },
  formWrapper: {
    background: '#ffffff',
    borderRadius: '16px',
    padding: '48px 40px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '420px',
    backdropFilter: 'blur(10px)',
  },
  header: {
    marginBottom: '32px',
  },
  logo: {
    height: '64px',
    marginBottom: '16px',
  },
  title: {
    margin: '0 0 8px 0',
    color: '#1a1a1a',
    fontWeight: 600,
  },
  subtitle: {
    fontSize: '16px',
    color: '#666',
  },
  alert: {
    borderRadius: '8px',
  },
  form: {
    width: '100%',
  },
  input: {
    borderRadius: '8px',
    height: '48px',
  },
  inputIcon: {
    color: '#999',
  },
  submitButton: {
    height: '48px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: 600,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    border: 'none',
    boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)',
  },
  demoCredentials: {
    marginTop: '16px',
    padding: '12px',
    background: '#f8f9fa',
    borderRadius: '8px',
    border: '1px solid #e9ecef',
  },
  demoText: {
    fontSize: '14px',
    fontFamily: 'monospace',
  },
};

export default LoginForm;
