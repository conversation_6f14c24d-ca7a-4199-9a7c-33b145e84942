import React from 'react';
import { Input as AntInput, InputProps as AntInputProps } from 'antd';

interface InputProps extends AntInputProps {
  label?: string;
  error?: string;
}

const Input: React.FC<InputProps> = ({ 
  label, 
  error, 
  style,
  ...props 
}) => {
  const inputStyle = {
    borderRadius: '8px',
    height: '40px',
    ...style,
  };

  return (
    <div style={{ marginBottom: '16px' }}>
      {label && (
        <label style={{ 
          display: 'block', 
          marginBottom: '8px', 
          fontWeight: 500,
          color: '#333'
        }}>
          {label}
        </label>
      )}
      <AntInput
        style={inputStyle}
        status={error ? 'error' : undefined}
        {...props}
      />
      {error && (
        <div style={{ 
          color: '#ff4d4f', 
          fontSize: '14px', 
          marginTop: '4px' 
        }}>
          {error}
        </div>
      )}
    </div>
  );
};

export default Input;
