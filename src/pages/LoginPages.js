import React from 'react';
import { Form, Input, Button, Card, Space } from 'antd';

const LoginPage = () => {
  return (
    <div style={styles.container}>
      <Card style={styles.card} bordered={false}>
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          {/* Company logo */}
          <img src="/favicon-black.png" alt="Company Logo" style={styles.logo} />

          {/* Login form */}
          <Form name="login_form" layout="vertical">
            <Form.Item
              name="username"
              label="Username"
              rules={[{ required: true, message: 'Please input your username!' }]}
            >
              <Input size="large" placeholder="Enter your username" />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[{ required: true, message: 'Please input your password!' }]}
            >
              <Input.Password size="large" placeholder="Enter your password" />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                block
                size="large"
                style={styles.button}
              >
                Log in
              </Button>
            </Form.Item>
          </Form>
        </Space>
      </Card>
    </div>
  );
};

// Styling for the page
const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    backgroundColor: '#f0f2f5',
  },
  card: {
    width: 400,
    padding: '40px 20px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    borderRadius: '8px',
    backgroundColor: '#fff',
  },
  logo: {
    display: 'block',
    margin: '0 auto',
    height: 80,
  },
  button: {
    backgroundColor: '#1890ff',
    borderColor: '#1890ff',
    fontWeight: 'bold',
  },
};

export default LoginPage;
