import React, { useState } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import LoginForm from '@/components/LoginForm';
import { authService } from '@/services';
import { LoginFormData } from '@/types';

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const navigate = useNavigate();

  const handleLogin = async (credentials: LoginFormData): Promise<void> => {
    setLoading(true);
    setError('');

    try {
      // Attempt login
      const response = await authService.login(credentials);

      if (response.access_token) {
        // Get user information after successful login
        try {
          const user = await authService.getCurrentUser();
          authService.storeUser(user);

          message.success('Login successful!');
          navigate('/dashboard');
        } catch (userError) {
          // If we can't get user info, still proceed with basic login
          console.warn('Could not fetch user info:', userError);
          message.success('Login successful!');
          navigate('/dashboard');
        }
      }
    } catch (err: any) {
      console.error('Login error:', err);

      let errorMessage = 'Login failed. Please try again.';

      if (err.response?.status === 401) {
        errorMessage = 'Invalid username or password.';
      } else if (err.response?.status === 422) {
        errorMessage = 'Please check your input and try again.';
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <LoginForm
      onSubmit={handleLogin}
      loading={loading}
      error={error}
    />
  );
};

export default LoginPage;
