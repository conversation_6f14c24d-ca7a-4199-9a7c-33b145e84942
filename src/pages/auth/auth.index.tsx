import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AuthLogin from './pages/auth.login';
import AuthSignup from './pages/auth.signup';

const AuthRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={<AuthLogin />} />
      <Route path="/signup" element={<AuthSignup />} />
      <Route path="/" element={<Navigate to="/login" replace />} />
    </Routes>
  );
};

export default AuthRouter;
