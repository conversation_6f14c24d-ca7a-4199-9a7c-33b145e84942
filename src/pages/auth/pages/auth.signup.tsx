import React, { useState } from 'react';
import { Form, Input, Button, Typography, Space, message } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const AuthSignup: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSignup = async (values: any) => {
    setLoading(true);
    try {
      // TODO: Implement signup API call
      console.log('Signup values:', values);
      message.success('Account created successfully!');
      navigate('/auth/login');
    } catch (error) {
      message.error('Signup failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.formWrapper}>
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
          <div style={styles.header}>
            <img 
              src="/final-logo-black.png" 
              alt="Company Logo" 
              style={styles.logo}
              onError={(e) => {
                (e.target as HTMLImageElement).src = "/favicon-black.png";
              }}
            />
            <Title level={2} style={styles.title}>
              Create Account
            </Title>
            <Text type="secondary" style={styles.subtitle}>
              Join our multi-tenant system
            </Text>
          </div>

          <Form
            name="signup"
            onFinish={handleSignup}
            layout="vertical"
            size="large"
            style={styles.form}
          >
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Please enter your username!' },
                { min: 3, message: 'Username must be at least 3 characters!' }
              ]}
            >
              <Input
                prefix={<UserOutlined style={styles.inputIcon} />}
                placeholder="Choose a username"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter your email!' },
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input
                prefix={<MailOutlined style={styles.inputIcon} />}
                placeholder="Enter your email"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please enter your password!' },
                { min: 6, message: 'Password must be at least 6 characters!' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={styles.inputIcon} />}
                placeholder="Create a password"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="Confirm Password"
              dependencies={['password']}
              rules={[
                { required: true, message: 'Please confirm your password!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Passwords do not match!'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={styles.inputIcon} />}
                placeholder="Confirm your password"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                style={styles.submitButton}
                block
              >
                {loading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </Form.Item>
          </Form>

          <div style={styles.loginLink}>
            <Text type="secondary">
              Already have an account?{' '}
              <Button type="link" onClick={() => navigate('/auth/login')} style={styles.linkButton}>
                Sign In
              </Button>
            </Text>
          </div>
        </Space>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    padding: '20px',
  },
  formWrapper: {
    background: '#ffffff',
    borderRadius: '16px',
    padding: '48px 40px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '420px',
    backdropFilter: 'blur(10px)',
  },
  header: {
    marginBottom: '32px',
  },
  logo: {
    height: '64px',
    marginBottom: '16px',
  },
  title: {
    margin: '0 0 8px 0',
    color: '#1a1a1a',
    fontWeight: 600,
  },
  subtitle: {
    fontSize: '16px',
    color: '#666',
  },
  form: {
    width: '100%',
  },
  input: {
    borderRadius: '8px',
    height: '48px',
  },
  inputIcon: {
    color: '#999',
  },
  submitButton: {
    height: '48px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: 600,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    border: 'none',
    boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)',
  },
  loginLink: {
    marginTop: '16px',
  },
  linkButton: {
    padding: 0,
    height: 'auto',
    color: '#667eea',
    fontWeight: 600,
  },
};

export default AuthSignup;
