import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Button, 
  Typography, 
  Space, 
  message, 
  Modal, 
  Form, 
  Input,
  Spin
} from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import DashboardLayout from '../components/dashboard.layout';
import DashboardTenantCard from '../components/dashboard.tenantCard';
import { tenantService } from '../../../services';
import { Tenant, CreateTenantRequest } from '../../../types';

const { Title, Text } = Typography;

const DashboardTenants: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    setLoading(true);
    try {
      const data = await tenantService.getTenants();
      setTenants(data);
    } catch (error) {
      console.error('Error fetching tenants:', error);
      message.error('Failed to load tenants');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTenant = async (values: CreateTenantRequest) => {
    setCreateLoading(true);
    try {
      await tenantService.createTenant(values);
      message.success('Tenant created successfully');
      setCreateModalVisible(false);
      form.resetFields();
      fetchTenants();
    } catch (error) {
      console.error('Error creating tenant:', error);
      message.error('Failed to create tenant');
    } finally {
      setCreateLoading(false);
    }
  };

  const handleViewTenant = (tenant: Tenant) => {
    message.info(`Viewing tenant: ${tenant.name}`);
    // TODO: Navigate to tenant details page
  };

  const handleEditTenant = (tenant: Tenant) => {
    message.info(`Editing tenant: ${tenant.name}`);
    // TODO: Open edit modal or navigate to edit page
  };

  const handleDeleteTenant = async (tenant: Tenant) => {
    Modal.confirm({
      title: 'Delete Tenant',
      content: `Are you sure you want to delete "${tenant.name}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await tenantService.deleteTenant(tenant.id);
          message.success(`Tenant ${tenant.name} deleted successfully`);
          fetchTenants();
        } catch (error) {
          message.error('Failed to delete tenant');
        }
      },
    });
  };

  return (
    <DashboardLayout>
      <div style={styles.container}>
        {/* Header */}
        <div style={styles.header}>
          <div>
            <Title level={2} style={styles.title}>
              Tenants
            </Title>
            <Text type="secondary" style={styles.subtitle}>
              Manage your organization's tenants
            </Text>
          </div>
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchTenants}
              loading={loading}
            >
              Refresh
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              Create Tenant
            </Button>
          </Space>
        </div>

        {/* Content */}
        {loading ? (
          <div style={styles.loadingContainer}>
            <Spin size="large" />
          </div>
        ) : (
          <Row gutter={[24, 24]}>
            {tenants.map((tenant) => (
              <Col xs={24} sm={12} lg={8} xl={6} key={tenant.id}>
                <DashboardTenantCard
                  tenant={tenant}
                  onView={handleViewTenant}
                  onEdit={handleEditTenant}
                  onDelete={handleDeleteTenant}
                />
              </Col>
            ))}
          </Row>
        )}

        {/* Create Tenant Modal */}
        <Modal
          title="Create New Tenant"
          open={createModalVisible}
          onCancel={() => {
            setCreateModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={500}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateTenant}
            style={styles.form}
          >
            <Form.Item
              name="name"
              label="Tenant Name"
              rules={[
                { required: true, message: 'Please enter tenant name!' },
                { min: 3, message: 'Name must be at least 3 characters!' }
              ]}
            >
              <Input 
                placeholder="Enter tenant name"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description"
              rules={[
                { max: 500, message: 'Description cannot exceed 500 characters!' }
              ]}
            >
              <Input.TextArea 
                placeholder="Enter tenant description (optional)"
                rows={4}
                style={styles.textarea}
              />
            </Form.Item>

            <Form.Item style={styles.formActions}>
              <Space>
                <Button 
                  onClick={() => {
                    setCreateModalVisible(false);
                    form.resetFields();
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  loading={createLoading}
                >
                  Create Tenant
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </DashboardLayout>
  );
};

const styles = {
  container: {
    padding: '0',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '32px',
  },
  title: {
    margin: '0 0 8px 0',
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: '16px',
  },
  loadingContainer: {
    textAlign: 'center' as const,
    padding: '50px',
  },
  form: {
    marginTop: '24px',
  },
  input: {
    borderRadius: '8px',
    height: '40px',
  },
  textarea: {
    borderRadius: '8px',
  },
  formActions: {
    marginBottom: '0',
    textAlign: 'right' as const,
  },
};

export default DashboardTenants;
