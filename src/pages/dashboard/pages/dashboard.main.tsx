import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button, message, Spin } from 'antd';
import { 
  UserOutlined, 
  ApartmentOutlined, 
  PlusOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/dashboard.layout';
import DashboardTenantCard from '../components/dashboard.tenantCard';
import { tenantService, authService } from '../../../services';
import { Tenant, User } from '../../../types';

const { Title, Text } = Typography;

const DashboardMain: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchData();
    const storedUser = authService.getStoredUser();
    setUser(storedUser);
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [tenantsData, usersData] = await Promise.all([
        tenantService.getTenants().catch(() => []),
        authService.getUsers().catch(() => [])
      ]);
      
      setTenants(tenantsData);
      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleViewTenant = (tenant: Tenant) => {
    message.info(`Viewing tenant: ${tenant.name}`);
    // TODO: Navigate to tenant details page
  };

  const handleEditTenant = (tenant: Tenant) => {
    message.info(`Editing tenant: ${tenant.name}`);
    // TODO: Open edit modal or navigate to edit page
  };

  const handleDeleteTenant = async (tenant: Tenant) => {
    try {
      await tenantService.deleteTenant(tenant.id);
      message.success(`Tenant ${tenant.name} deleted successfully`);
      fetchData(); // Refresh data
    } catch (error) {
      message.error('Failed to delete tenant');
    }
  };

  const handleCreateTenant = () => {
    navigate('/dashboard/tenants');
  };

  const handleManageUsers = () => {
    navigate('/dashboard/users');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div style={styles.container}>
        {/* Welcome Section */}
        <div style={styles.welcomeSection}>
          <Title level={2} style={styles.welcomeTitle}>
            Welcome back, {user?.username || 'Admin'}!
          </Title>
          <Text type="secondary" style={styles.welcomeSubtitle}>
            Here's what's happening with your multi-tenant system today.
          </Text>
        </div>

        {/* Statistics Cards */}
        <Row gutter={[24, 24]} style={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            <Card style={styles.statCard}>
              <Statistic
                title="Total Tenants"
                value={tenants.length}
                prefix={<ApartmentOutlined style={styles.statIcon} />}
                valueStyle={styles.statValue}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card style={styles.statCard}>
              <Statistic
                title="Active Tenants"
                value={tenants.filter(t => t.status === 'active').length}
                prefix={<ApartmentOutlined style={styles.statIconGreen} />}
                valueStyle={styles.statValueGreen}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card style={styles.statCard}>
              <Statistic
                title="Total Users"
                value={users.length}
                prefix={<UserOutlined style={styles.statIcon} />}
                valueStyle={styles.statValue}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card style={styles.statCard}>
              <Statistic
                title="Admin Users"
                value={users.filter(u => u.role === 'admin').length}
                prefix={<UserOutlined style={styles.statIconBlue} />}
                valueStyle={styles.statValueBlue}
              />
            </Card>
          </Col>
        </Row>

        {/* Tenants Section */}
        <div style={styles.section}>
          <div style={styles.sectionHeader}>
            <div>
              <Title level={3} style={styles.sectionTitle}>
                Tenants
              </Title>
              <Text type="secondary">
                Manage your organization's tenants
              </Text>
            </div>
            <Space>
              <Button 
                icon={<EyeOutlined />}
                onClick={() => navigate('/dashboard/tenants')}
              >
                View All
              </Button>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleCreateTenant}
              >
                Create New
              </Button>
            </Space>
          </div>

          {tenants.length > 0 ? (
            <Row gutter={[24, 24]}>
              {tenants.slice(0, 6).map((tenant) => (
                <Col xs={24} sm={12} lg={8} key={tenant.id}>
                  <DashboardTenantCard
                    tenant={tenant}
                    onView={handleViewTenant}
                    onEdit={handleEditTenant}
                    onDelete={handleDeleteTenant}
                  />
                </Col>
              ))}
            </Row>
          ) : (
            <Card style={styles.emptyCard}>
              <div style={styles.emptyState}>
                <ApartmentOutlined style={styles.emptyIcon} />
                <Title level={4} style={styles.emptyTitle}>
                  No tenants found
                </Title>
                <Text type="secondary" style={styles.emptyText}>
                  Get started by creating your first tenant
                </Text>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleCreateTenant}
                  style={styles.emptyButton}
                >
                  Create Tenant
                </Button>
              </div>
            </Card>
          )}
        </div>

        {/* User Management Section */}
        <div style={styles.section}>
          <div style={styles.sectionHeader}>
            <div>
              <Title level={3} style={styles.sectionTitle}>
                User Management
              </Title>
              <Text type="secondary">
                Manage system users and permissions
              </Text>
            </div>
            <Button 
              type="primary" 
              icon={<UserOutlined />}
              onClick={handleManageUsers}
            >
              Manage Users
            </Button>
          </div>

          <Card style={styles.userManagementCard}>
            <Row gutter={[24, 24]}>
              <Col xs={24} md={12}>
                <div style={styles.userStat}>
                  <UserOutlined style={styles.userStatIcon} />
                  <div>
                    <Text strong style={styles.userStatNumber}>
                      {users.length}
                    </Text>
                    <Text type="secondary" style={styles.userStatLabel}>
                      Total Users
                    </Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={12}>
                <div style={styles.userStat}>
                  <UserOutlined style={styles.userStatIconAdmin} />
                  <div>
                    <Text strong style={styles.userStatNumber}>
                      {users.filter(u => u.role === 'admin').length}
                    </Text>
                    <Text type="secondary" style={styles.userStatLabel}>
                      Admin Users
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

const styles = {
  container: {
    padding: '0',
  },
  welcomeSection: {
    marginBottom: '32px',
  },
  welcomeTitle: {
    margin: '0 0 8px 0',
    color: '#1a1a1a',
  },
  welcomeSubtitle: {
    fontSize: '16px',
  },
  statsRow: {
    marginBottom: '32px',
  },
  statCard: {
    borderRadius: '12px',
    border: '1px solid #f0f0f0',
  },
  statIcon: {
    color: '#1890ff',
  },
  statIconGreen: {
    color: '#52c41a',
  },
  statIconBlue: {
    color: '#1890ff',
  },
  statValue: {
    color: '#1890ff',
  },
  statValueGreen: {
    color: '#52c41a',
  },
  statValueBlue: {
    color: '#1890ff',
  },
  section: {
    marginBottom: '32px',
  },
  sectionHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '24px',
  },
  sectionTitle: {
    margin: '0 0 4px 0',
  },
  emptyCard: {
    borderRadius: '12px',
    border: '1px solid #f0f0f0',
  },
  emptyState: {
    textAlign: 'center' as const,
    padding: '48px 24px',
  },
  emptyIcon: {
    fontSize: '48px',
    color: '#d9d9d9',
    marginBottom: '16px',
  },
  emptyTitle: {
    margin: '0 0 8px 0',
    color: '#999',
  },
  emptyText: {
    marginBottom: '24px',
  },
  emptyButton: {
    borderRadius: '8px',
  },
  userManagementCard: {
    borderRadius: '12px',
    border: '1px solid #f0f0f0',
  },
  userStat: {
    display: 'flex',
    alignItems: 'center',
    padding: '16px',
  },
  userStatIcon: {
    fontSize: '32px',
    color: '#1890ff',
    marginRight: '16px',
  },
  userStatIconAdmin: {
    fontSize: '32px',
    color: '#52c41a',
    marginRight: '16px',
  },
  userStatNumber: {
    display: 'block',
    fontSize: '24px',
    color: '#1a1a1a',
  },
  userStatLabel: {
    display: 'block',
    fontSize: '14px',
  },
};

export default DashboardMain;
