import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Typography, 
  Space, 
  Tag, 
  message, 
  Modal, 
  Form, 
  Input,
  Select,
  Avatar,
  Tooltip
} from 'antd';
import { 
  PlusOutlined, 
  ReloadOutlined, 
  UserOutlined, 
  EditOutlined, 
  DeleteOutlined 
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import DashboardLayout from '../components/dashboard.layout';
import { authService } from '../../../services';
import { User, CreateUserFormData } from '../../../types';

const { Title, Text } = Typography;
const { Option } = Select;

const DashboardUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const data = await authService.getUsers();
      setUsers(data);
    } catch (error) {
      console.error('Error fetching users:', error);
      message.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (values: CreateUserFormData) => {
    setCreateLoading(true);
    try {
      // TODO: Implement create user API call
      console.log('Creating user:', values);
      message.success('User created successfully');
      setCreateModalVisible(false);
      form.resetFields();
      fetchUsers();
    } catch (error) {
      console.error('Error creating user:', error);
      message.error('Failed to create user');
    } finally {
      setCreateLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    message.info(`Editing user: ${user.username}`);
    // TODO: Open edit modal
  };

  const handleDeleteUser = (user: User) => {
    Modal.confirm({
      title: 'Delete User',
      content: `Are you sure you want to delete user "${user.username}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          // TODO: Implement delete user API call
          message.success(`User ${user.username} deleted successfully`);
          fetchUsers();
        } catch (error) {
          message.error('Failed to delete user');
        }
      },
    });
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'red';
      case 'manager':
        return 'orange';
      case 'user':
        return 'blue';
      default:
        return 'default';
    }
  };

  const columns: ColumnsType<User> = [
    {
      title: 'User',
      dataIndex: 'username',
      key: 'username',
      render: (username: string, record: User) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{username}</div>
            {record.email && (
              <div style={{ fontSize: '12px', color: '#999' }}>
                {record.email}
              </div>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={getRoleColor(role)} style={{ textTransform: 'capitalize' }}>
          {role}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => 
        date ? new Date(date).toLocaleDateString() : 'N/A',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: User) => (
        <Space>
          <Tooltip title="Edit User">
            <Button 
              type="text" 
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Tooltip title="Delete User">
            <Button 
              type="text" 
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteUser(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <DashboardLayout>
      <div style={styles.container}>
        {/* Header */}
        <div style={styles.header}>
          <div>
            <Title level={2} style={styles.title}>
              User Management
            </Title>
            <Text type="secondary" style={styles.subtitle}>
              Manage system users and their permissions
            </Text>
          </div>
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchUsers}
              loading={loading}
            >
              Refresh
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              Create User
            </Button>
          </Space>
        </div>

        {/* Users Table */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} users`,
          }}
          style={styles.table}
        />

        {/* Create User Modal */}
        <Modal
          title="Create New User"
          open={createModalVisible}
          onCancel={() => {
            setCreateModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={500}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateUser}
            style={styles.form}
          >
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Please enter username!' },
                { min: 3, message: 'Username must be at least 3 characters!' }
              ]}
            >
              <Input 
                placeholder="Enter username"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter email!' },
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input 
                placeholder="Enter email address"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please enter password!' },
                { min: 6, message: 'Password must be at least 6 characters!' }
              ]}
            >
              <Input.Password 
                placeholder="Enter password"
                style={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="role"
              label="Role"
              rules={[
                { required: true, message: 'Please select a role!' }
              ]}
            >
              <Select 
                placeholder="Select user role"
                style={styles.select}
              >
                <Option value="admin">Admin</Option>
                <Option value="manager">Manager</Option>
                <Option value="user">User</Option>
              </Select>
            </Form.Item>

            <Form.Item style={styles.formActions}>
              <Space>
                <Button 
                  onClick={() => {
                    setCreateModalVisible(false);
                    form.resetFields();
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  loading={createLoading}
                >
                  Create User
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </DashboardLayout>
  );
};

const styles = {
  container: {
    padding: '0',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '32px',
  },
  title: {
    margin: '0 0 8px 0',
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: '16px',
  },
  table: {
    background: '#fff',
    borderRadius: '8px',
  },
  form: {
    marginTop: '24px',
  },
  input: {
    borderRadius: '8px',
    height: '40px',
  },
  select: {
    borderRadius: '8px',
    height: '40px',
  },
  formActions: {
    marginBottom: '0',
    textAlign: 'right' as const,
  },
};

export default DashboardUsers;
