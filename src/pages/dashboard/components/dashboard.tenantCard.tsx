import React from 'react';
import { <PERSON>, Button, Tag, Space, Typography, Dropdown } from 'antd';
import { 
  EyeOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  MoreOutlined,
  CalendarOutlined,
  UserOutlined
} from '@ant-design/icons';
import { Tenant } from '../../../types';

const { Text, Title } = Typography;

interface TenantCardProps {
  tenant: Tenant;
  onView?: (tenant: Tenant) => void;
  onEdit?: (tenant: Tenant) => void;
  onDelete?: (tenant: Tenant) => void;
}

const DashboardTenantCard: React.FC<TenantCardProps> = ({
  tenant,
  onView,
  onEdit,
  onDelete,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'orange';
      case 'suspended':
        return 'red';
      default:
        return 'default';
    }
  };

  const menuItems = [
    {
      key: 'view',
      icon: <EyeOutlined />,
      label: 'View Details',
      onClick: () => onView?.(tenant),
    },
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: 'Edit',
      onClick: () => onEdit?.(tenant),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: 'Delete',
      danger: true,
      onClick: () => onDelete?.(tenant),
    },
  ];

  return (
    <Card
      style={styles.card}
      bodyStyle={styles.cardBody}
      hoverable
      actions={[
        <Button 
          type="primary" 
          icon={<EyeOutlined />}
          onClick={() => onView?.(tenant)}
          style={styles.viewButton}
        >
          View
        </Button>,
        <Dropdown
          menu={{ items: menuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button 
            type="text" 
            icon={<MoreOutlined />}
            style={styles.moreButton}
          />
        </Dropdown>
      ]}
    >
      <div style={styles.cardHeader}>
        <div>
          <Title level={4} style={styles.tenantName}>
            {tenant.name}
          </Title>
          <Tag color={getStatusColor(tenant.status)} style={styles.statusTag}>
            {tenant.status.toUpperCase()}
          </Tag>
        </div>
      </div>

      {tenant.description && (
        <Text type="secondary" style={styles.description}>
          {tenant.description}
        </Text>
      )}

      <div style={styles.metadata}>
        <Space direction="vertical" size="small">
          <div style={styles.metaItem}>
            <CalendarOutlined style={styles.metaIcon} />
            <Text type="secondary" style={styles.metaText}>
              Created: {new Date(tenant.created_at).toLocaleDateString()}
            </Text>
          </div>
          
          {tenant.owner_id && (
            <div style={styles.metaItem}>
              <UserOutlined style={styles.metaIcon} />
              <Text type="secondary" style={styles.metaText}>
                Owner: {tenant.owner_id}
              </Text>
            </div>
          )}
        </Space>
      </div>
    </Card>
  );
};

const styles = {
  card: {
    borderRadius: '12px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    border: '1px solid #f0f0f0',
    transition: 'all 0.3s ease',
  },
  cardBody: {
    padding: '20px',
  },
  cardHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '12px',
  },
  tenantName: {
    margin: '0 0 8px 0',
    color: '#1a1a1a',
    fontSize: '18px',
  },
  statusTag: {
    fontSize: '11px',
    fontWeight: 600,
    borderRadius: '4px',
  },
  description: {
    display: 'block',
    marginBottom: '16px',
    lineHeight: '1.5',
    fontSize: '14px',
  },
  metadata: {
    marginTop: '16px',
    paddingTop: '16px',
    borderTop: '1px solid #f0f0f0',
  },
  metaItem: {
    display: 'flex',
    alignItems: 'center',
  },
  metaIcon: {
    marginRight: '8px',
    color: '#999',
    fontSize: '12px',
  },
  metaText: {
    fontSize: '12px',
  },
  viewButton: {
    borderRadius: '6px',
    fontWeight: 600,
  },
  moreButton: {
    color: '#999',
  },
};

export default DashboardTenantCard;
