import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Typography, Space, Button, message } from 'antd';
import { 
  UserOutlined, 
  LogoutOutlined, 
  DashboardOutlined, 
  TeamOutlined, 
  ApartmentOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { authService } from '../../../services';
import { User } from '../../../types';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Get user info from localStorage or API
    const storedUser = authService.getStoredUser();
    if (storedUser) {
      setUser(storedUser);
    } else {
      // Try to fetch user info from API
      fetchUserInfo();
    }
  }, []);

  const fetchUserInfo = async () => {
    try {
      const userInfo = await authService.getCurrentUser();
      setUser(userInfo);
      authService.storeUser(userInfo);
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      // If we can't get user info, create a basic user object
      setUser({
        id: '1',
        username: 'admin',
        role: 'admin'
      });
    }
  };

  const handleLogout = () => {
    authService.logout();
    message.success('Logged out successfully');
    navigate('/auth/login');
  };

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: '/dashboard/tenants',
      icon: <ApartmentOutlined />,
      label: 'Tenants',
    },
    {
      key: '/dashboard/users',
      icon: <TeamOutlined />,
      label: 'Users',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
        }}
      >
        <div style={styles.logo}>
          <img 
            src="/final-logo-black.png" 
            alt="Logo" 
            style={styles.logoImage}
            onError={(e) => {
              (e.target as HTMLImageElement).src = "/favicon-black.png";
            }}
          />
          {!collapsed && (
            <Text strong style={styles.logoText}>
              Multi-Tenant
            </Text>
          )}
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>
      
      <Layout>
        <Header style={styles.header}>
          <div style={styles.headerLeft}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={styles.collapseButton}
            />
          </div>
          
          <div style={styles.headerRight}>
            <Space>
              <Text type="secondary">Welcome back,</Text>
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space style={styles.userInfo}>
                  <Avatar 
                    icon={<UserOutlined />} 
                    style={styles.avatar}
                  />
                  <div style={styles.userDetails}>
                    <Text strong>{user?.username || 'User'}</Text>
                    <Text type="secondary" style={styles.userRole}>
                      {user?.role || 'admin'}
                    </Text>
                  </div>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>
        
        <Content style={styles.content}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

const styles = {
  logo: {
    height: '64px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '16px',
    borderBottom: '1px solid #f0f0f0',
    marginBottom: '16px',
  },
  logoImage: {
    height: '32px',
    marginRight: '8px',
  },
  logoText: {
    fontSize: '16px',
    color: '#1890ff',
  },
  header: {
    background: '#fff',
    padding: '0 24px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    zIndex: 1,
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
  },
  collapseButton: {
    fontSize: '16px',
    width: '40px',
    height: '40px',
  },
  userInfo: {
    cursor: 'pointer',
    padding: '8px 12px',
    borderRadius: '8px',
    transition: 'background-color 0.3s',
  },
  avatar: {
    backgroundColor: '#1890ff',
  },
  userDetails: {
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'flex-start',
  },
  userRole: {
    fontSize: '12px',
    textTransform: 'capitalize' as const,
  },
  content: {
    margin: '24px',
    padding: '24px',
    background: '#fff',
    borderRadius: '8px',
    minHeight: 'calc(100vh - 112px)',
  },
};

export default DashboardLayout;
