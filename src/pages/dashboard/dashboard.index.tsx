import React from 'react';
import { Routes, Route } from 'react-router-dom';
import DashboardMain from './pages/dashboard.main';
import DashboardUsers from './pages/dashboard.users';
import DashboardTenants from './pages/dashboard.tenants';

const DashboardRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<DashboardMain />} />
      <Route path="/users" element={<DashboardUsers />} />
      <Route path="/tenants" element={<DashboardTenants />} />
    </Routes>
  );
};

export default DashboardRouter;
