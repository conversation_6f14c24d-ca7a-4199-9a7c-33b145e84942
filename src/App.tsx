import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import AuthRouter from './pages/auth/auth.index';
import DashboardRouter from './pages/dashboard/dashboard.index';
import { authService } from './services';

const App: React.FC = () => {
  const isAuthenticated = authService.isAuthenticated();

  return (
    <Router>
      <Routes>
        <Route
          path="/auth/*"
          element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthRouter />}
        />
        <Route
          path="/dashboard/*"
          element={isAuthenticated ? <DashboardRouter /> : <Navigate to="/auth/login" replace />}
        />
        <Route
          path="/"
          element={<Navigate to={isAuthenticated ? "/dashboard" : "/auth/login"} replace />}
        />
      </Routes>
    </Router>
  );
};

export default App;
